import { LocaleEnum } from '@/enums/locale.enum'
import { TextToSpeech } from '@/types/audio.type'
import { Faculty, SampleSentences } from '@/types/faculty.type'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { ESymptomsType, MedicalSentencePayload } from '../types'

export type KeywordByType = Keyword & {
  type: ESymptomsType
}

export interface MedicalFacultiesState {
  isLoading: boolean
  type: 'patient' | 'doctor'
  patientSentence: Record<string, string>
  doctorSentence: Record<string, string>
  keywordsPatient: Record<string, Keyword>
  keywordsDoctor: Record<string, Keyword>
  keywordByType: Record<string, string>
  isEditPatientMode: boolean
  isEditDoctorMode: boolean
  facultyDetail: Faculty | null
  sampleSentencesPatient: SampleSentences[]
  sampleSentencesDoctor: SampleSentences[]
  audio: Record<string, TextToSpeech>
  idReport: string
}

export interface MedicalFacultiesActions {
  hiddenLoading: () => void
  showLoading: () => void
  isFinishInType: () => boolean
  setType: (type: 'patient' | 'doctor') => void
  setSentence: (sentence: Record<string, string>) => void
  setKeywords: (keywords: Record<string, Keyword>) => void
  saveFacultyDetail: (faculty: Faculty) => void
  saveAudio: (audio: TextToSpeech, language: LocaleEnum) => void
  saveIdRecord: (id: string) => void
  getSentence: () => Record<string, string>
  getKeywords: () => Record<string, Keyword>
  getStatusEditMode: () => boolean
  getSampleSentences: () => SampleSentences[]
  getSentenceAudio: (language: LocaleEnum) => string
  getPayloadMedicalSentence: () => MedicalSentencePayload
  updateSentence: (keywords: Record<string, Keyword>) => void
  updateStatusEditMode: (status: boolean) => void
  reset: () => void
}

const initialState: MedicalFacultiesState = {
  isLoading: false,
  type: 'patient',
  patientSentence: {},
  doctorSentence: {},
  keywordsPatient: {},
  keywordsDoctor: {},
  keywordByType: {},
  isEditPatientMode: false,
  isEditDoctorMode: false,
  facultyDetail: null,
  sampleSentencesPatient: [],
  sampleSentencesDoctor: [],
  audio: {},
  idReport: ''
}

export const useMedicalFacultiesStore = create<MedicalFacultiesState & MedicalFacultiesActions>()(
  devtools((set, get) => ({
    ...initialState,
    hiddenLoading: () => {
      set({ isLoading: false })
    },
    showLoading: () => {
      set({ isLoading: true })
    },
    setType: (type: 'patient' | 'doctor') => {
      set({ type })
    },
    setSentence: (sentence: Record<string, string>) => {
      set((state) => {
        console.log("state", state)
        return ({
          ...state,
          [state.type === 'patient' ? 'patientSentence' : 'doctorSentence']: sentence,
        })
      })
    },
    setKeywords: (keywords: Record<string, Keyword>) => {
      set((state) => ({
        ...state,
        keywordByType: handleConvertKeywordByType(keywords),
        [state.type === 'patient' ? 'keywordsPatient' : 'keywordsDoctor']: keywords,
      }))
    },
    isFinishInType: () => {
      return get().type === 'doctor'
        ? Object.keys(get().keywordsDoctor).length > 0
        : Object.keys(get().keywordsPatient).length > 0
    },
    reset: () => {
      set(initialState)
    },

    getSentence: () => {
      return get().type === 'patient' ? get().patientSentence : get().doctorSentence
    },
    getKeywords: () => {
      return get().type === 'patient' ? get().keywordsPatient : get().keywordsDoctor
    },
    updateSentence: (keywords: Record<string, Keyword>) => {
      const oldIdToTypeMap = get().keywordByType
      const newIdToTypeMap = handleConvertKeywordByType(keywords)
      const newSentence = updateTemplateKeywords(
        get().getSentence(),
        newIdToTypeMap,
        oldIdToTypeMap,
      )
      set((state) => {
        const isPatient = state.type === 'patient'

        return {
          [isPatient ? 'patientSentence' : 'doctorSentence']: newSentence,
          [isPatient ? 'keywordsPatient' : 'keywordsDoctor']: keywords,
          keywordByType: newIdToTypeMap,
        }
      })
    },
    updateStatusEditMode: (status: boolean) => {
      set((state) => {
        const isPatient = state.type === 'patient'
        return {
          [isPatient ? 'isEditPatientMode' : 'isEditDoctorMode']: status,
        }
      })
    },
    getStatusEditMode: () => {
      return get().type === 'patient' ? get().isEditPatientMode : get().isEditDoctorMode
    },
    saveFacultyDetail: (faculty: Faculty) => {
      const sampleSentences = faculty.sampleSentences || []
      const { doctorSamples, patientSamples } = handleFilterSampleSentenceByType(sampleSentences)

      set({
        facultyDetail: faculty,
        sampleSentencesPatient: patientSamples,
        sampleSentencesDoctor: doctorSamples,
      })
    },
    getSampleSentences: () => {
      return get().type === 'patient' ? get().sampleSentencesPatient : get().sampleSentencesDoctor
    },
    saveAudio: (audio: TextToSpeech, language: LocaleEnum) => {
      set((state) => ({
        audio: {
          ...state.audio,
          [language]: audio,
        },
      }))
    },

    getSentenceAudio: (language: LocaleEnum) => {
      const sentence = get().type === 'patient' ? get().patientSentence : get().doctorSentence

      const sentenceConvert = handleRemoveIdByKeyword(
        get().getKeywords(),
        sentence[language],
        language,
      )
      return sentenceConvert || ''
    },
    getPayloadMedicalSentence: () => {
      const payload: MedicalSentencePayload = {
        sentenceResult: [
          {
            type: 'patient',
            sentence: {
              speech: get().patientSentence,
              keywords: get().keywordsPatient,
            },
          },
          {
            type: 'doctor',
            sentence: {
              speech: get().doctorSentence,
              keywords: get().keywordsDoctor,
            },
          },
        ],
        sentenceAudio: Object.values(LocaleEnum)
          .map((language) => get().audio[language]?.audioId ? ({
            language,
            audio: get().audio[language]?.audioId || '',
          }) : null)
          .filter((item): item is { language: LocaleEnum; audio: string } => item !== null),
      }
      return payload
    },
    saveIdRecord: (idReport: string) => {
      set({ idReport })
    },
  })),
)

const handleRemoveIdByKeyword = (
  keywords: Record<string, Keyword>,
  sentence: string,
  language: LocaleEnum,
) => {
  if (!sentence) return ''
  const regex = /\[([^\]]+)\]/g
  return sentence.replace(regex, (match, id) => {
    if (keywords[id]) {
      return (keywords[id].name as unknown as LocalizeField<string>)[language as LocaleEnum]
    }
    return id
  })
}

const handleFilterSampleSentenceByType = (sampleSentences: SampleSentences[]) => {
  return sampleSentences.reduce(
    (acc, sentence) => {
      if (sentence.type && sentence.type === 'doctor') {
        acc.doctorSamples.push(sentence)
      } else {
        acc.patientSamples.push(sentence)
      }
      return acc
    },
    {
      doctorSamples: [] as SampleSentences[],
      patientSamples: [] as SampleSentences[],
    },
  )
}

const handleConvertKeywordByType = (keywords: Record<string, Keyword>): Record<string, string> => {
  const keywordByType: Record<string, string> = {}
  Object.values(keywords).forEach((keyword) => {
    const keywordType = keyword as KeywordByType
    if (keywordType.type) {
      keywordByType[keywordType.id] = keywordType.type
    }
  })
  return keywordByType
}

const groupIdsByType = (idToTypeMap: Record<string, string>) => {
  return Object.entries(idToTypeMap).reduce<Record<string, string[]>>((acc, [id, type]) => {
    ; (acc[type] ||= []).push(id)
    return acc
  }, {})
}

const updateTemplateKeywords = (
  templates: Record<string, string>,
  newIdToTypeMap: Record<string, string>,
  oldIdToTypeMap: Record<string, string>,
) => {
  const newKeywordsByType = groupIdsByType(newIdToTypeMap)
  const updatedTemplates: Record<string, string> = {}

  const extractOldKeywordMatches = (template: string) => {
    const regex = /\[([^\]]+)\]/g
    const matches: { match: string; id: string; type: string; start: number; end: number }[] = []

    let m
    while ((m = regex.exec(template)) !== null) {
      const id = m[1]
      const type = oldIdToTypeMap[id]
      if (!type) continue

      matches.push({
        match: m[0],
        id,
        type,
        start: m.index,
        end: m.index + m[0].length,
      })
    }

    return matches
  }

  for (const [lang, template] of Object.entries(templates)) {
    const matches = extractOldKeywordMatches(template)
    if (matches.length === 0) {
      updatedTemplates[lang] = template
      continue
    }

    let result = template
    const processedTypes = new Set<string>()

    for (const { type } of matches) {
      if (processedTypes.has(type)) continue

      const typeMatches = matches.filter((m) => m.type === type)
      const first = typeMatches[0]
      const last = typeMatches[typeMatches.length - 1]

      const replaceZone = template.slice(first.start, last.end)
      const newIds = newKeywordsByType[type] ?? []
      const replacement = newIds.map((id) => `[${id}]`).join(' ')

      const escaped = replaceZone.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
      result = result.replace(new RegExp(escaped, 'g'), replacement)

      processedTypes.add(type)
    }

    updatedTemplates[lang] = result
  }

  return updatedTemplates
}
