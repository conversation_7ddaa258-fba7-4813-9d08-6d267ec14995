'use client'
import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native'

import CheckboxActiveIcon from '@/assets/icons/checkbox-active.svg'
import CheckboxInactiveIcon from '@/assets/icons/checkbox-inactive.svg'
import EmptyBoxIcon from '@/assets/icons/empty-box.svg'

import { LocaleEnum } from '@/enums/locale.enum'
import {
  ListItem,
  useMedicalDictionaryList,
} from '@/features/medical-dictionary/hooks/common/useMedicalDictionaryList'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { useCallback, useEffect, useState } from 'react'

type KeywordSelectionLayoutProps = {
  keywordSelected: Record<string, Keyword>
  isShowCancelButton?: boolean
  onApply: (keywordSelected: Record<string, Keyword>) => void
  onCancel?: () => void
}
export const KeywordSelectionLayout: React.FC<KeywordSelectionLayoutProps> = ({
  keywordSelected: initialKeywordSelected,
  isShowCancelButton,
  onCancel,
  onApply,
}) => {
  const { t } = useTranslation()

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const [keywordSelected, setKeywordSelected] = useState<Record<string, Keyword>>({})

  useEffect(() => {
    setKeywordSelected(initialKeywordSelected)
  }, [initialKeywordSelected])

  const {
    refreshing,
    data,
    isGetKeywordsError,
    handleRefresh,
    handleLoadMore,
    // isGetKeywordsLoading,
    isFetchingNextPage,
  } = useMedicalDictionaryList({
    searchTextValue: '',
    config: {
      staleTime: 0,
      gcTime: 0,
      enabled: true,
    },
  })

  console.log(data[1])

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'keyword') {
      return `keyword-${item.item.id}`
    }
    if (item.type === 'loading') {
      return `loading-${index}`
    }
    return `item-${index}`
  }, [])

  const handleSelectedKeyword = (keyword: Keyword) => {
    setKeywordSelected((prev) => {
      if (prev[keyword.id]) {
        const newSelected = { ...prev }
        delete newSelected[keyword.id]
        return newSelected
      }
      return {
        ...prev,
        [keyword.id]: keyword,
      }
    })
  }

  return (
    <View className="h-full bg-white">
      <View className="w-full px-3">
        <TextInput
          placeholder={t('MES-67')}
          className="rounded-lg border  border-custom-divider px-4 py-3"
        />
      </View>

      <FlatList
        className="h-full px-3"
        data={data}
        extraData={keywordSelected}
        renderItem={({ item }) =>
          RenderKeyword({
            item,
            primaryLanguage,
            refreshing,
            secondaryLanguage,
            keywordSelected,
            onKeywordSelected: handleSelectedKeyword,
          })
        }
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        onEndReached={handleLoadMore}
        ListEmptyComponent={() => renderEmptyComponent(isGetKeywordsError, t)}
        ListFooterComponent={() => renderFooter(isFetchingNextPage)}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        onEndReachedThreshold={0.1}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary['500']]}
            tintColor={colors.primary['500']}
            progressBackgroundColor="#FFFFFF"
            // Add these properties to improve refresh behavior
            progressViewOffset={0}
          />
        }
      ></FlatList>

      <View
        className="sticky bottom-0 bg-white pt-3"
        style={[styles.shadowMd, { paddingBottom: 48 }]}
      >
        <View className="flex-row items-center gap-2 px-3">
          {isShowCancelButton ? (
            <TouchableOpacity
              className="flex-1 items-center justify-center rounded-xl bg-custom-neutral-80 py-2 text-center"
              onPress={() => onCancel?.()}
            >
              <Text size="body10" variant="default">
                {t('MES-45')}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              disabled={Object.keys(keywordSelected).length === 0}
              onPress={() => {
                if (Object.keys(keywordSelected).length === 0) return
                setKeywordSelected({})
              }}
              className={`flex-1 ${Object.keys(keywordSelected).length === 0 ? 'opacity-50' : ''} items-center justify-center rounded-xl bg-custom-neutral-80 py-2 text-center`}
            >
              <Text size="body10" variant="default">
                {t('MES-105')}
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            onPress={() => onApply(keywordSelected)}
            className="flex-1 items-center justify-center rounded-xl bg-primary-500 py-2 text-center"
          >
            <Text size="body10" variant="white">
              {t('MES-281')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const RenderKeyword = ({
  item,
  primaryLanguage,
  secondaryLanguage,
  refreshing,
  keywordSelected,
  onKeywordSelected,
}: {
  item: ListItem
  primaryLanguage: string
  refreshing: boolean
  secondaryLanguage: string
  keywordSelected: Record<string, Keyword>
  onKeywordSelected: (keywordSelected: Keyword) => void
}) => {
  if (item.type === 'loading_skeleton' && !refreshing) {
    return (
      <View className="items-center py-4">
        <ActivityIndicator size="small" />
      </View>
    )
  }

  if (item.type !== 'keyword') return null

  const { name, id } = item.item
  const localizedName = name as unknown as LocalizeField<string>
  return (
    <TouchableOpacity onPress={() => onKeywordSelected(item.item)}>
      <View className="flex-row items-start gap-3 border-b border-custom-divider py-2">
        <View className="mt-1">
          <CheckBoxIcon status={keywordSelected?.[id] ? true : false} />
        </View>

        <View className="flex-1">
          <Text size="body6">{localizedName[primaryLanguage as LocaleEnum]}</Text>

          <Text size="body7" variant="subdued">
            {localizedName[secondaryLanguage as LocaleEnum]}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const renderEmptyComponent = (isGetKeywordsError: boolean, t: (key: string) => string) => {
  if (isGetKeywordsError) {
    return (
      <View className="items-center  py-8">
        <Text size="body6" className="text-custom-danger-600">
          {t('MES-614')}
        </Text>
      </View>
    )
  }

  return (
    <View className="flex flex-col items-center justify-center gap-y-2 py-8">
      <EmptyBoxIcon />
      <Text size="body6" variant="default">
        {t('MES-768')}
      </Text>
    </View>
  )
}

const renderFooter = (isFetchingNextPage: boolean) => {
  if (!isFetchingNextPage) return null

  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  )
}

const CheckBoxIcon: React.FC<{ status?: boolean }> = ({ status = false }) => {
  return status ? (
    <CheckboxActiveIcon width={16} height={16} />
  ) : (
    <CheckboxInactiveIcon width={16} height={16} />
  )
}

const styles = StyleSheet.create({
  shadowMd: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.6,
    shadowRadius: 6,
  },
})
