import { useMemo } from 'react'
import { useDataStreamTranscriptions } from '../../hooks/use-data-stream-transcription'
import { DescriptionBox } from './DescriptionBox'

type DescriptionBoxWithLiveKitProps = {
  isEditable?: boolean
  isAgentConnected: boolean
  valueDescription: string
  onChangeValueDescription: (value: string) => void
  onToggleConnection: () => void
}

/**
 * Wrapper component that provides LiveKit hooks to DescriptionBox
 * This component should only be rendered inside LiveKitRoom context
 */
export const DescriptionBoxWithLiveKit: React.FC<DescriptionBoxWithLiveKitProps> = (props) => {
  const { fullTranscript, clearTranscript } = useDataStreamTranscriptions()

  // Dynamic require to avoid importing LiveKit in Expo Go
  //eslint-disable-next-line @typescript-eslint/no-require-imports
  const { useLocalParticipant, useVoiceAssistant } = require('@livekit/react-native')
  const { localParticipant, isMicrophoneEnabled } = useLocalParticipant()
  const { agent, state } = useVoiceAssistant()
  const isAgentLoading = useMemo(() => {
    return props.isAgentConnected && !agent && state === 'connecting'
  }, [props.isAgentConnected, agent, state])

  return (
    <DescriptionBox
      {...props}
      fullTranscript={fullTranscript}
      clearTranscript={clearTranscript}
      localParticipant={localParticipant}
      isMicrophoneEnabled={isMicrophoneEnabled}
      isAgentLoading={isAgentLoading}
    />
  )
}
