import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, TouchableOpacity, View } from 'react-native'

import SoundMute from '@/assets/icons/sound-mute.svg'
import VolumeIcon from '@/assets/icons/volume-high.svg'

import EditIcon from '@/assets/icons/edit-gray.svg'
import TrashIcon from '@/assets/icons/trash-gray.svg'
import { useOpenBaseWarningPopup } from '@/components/Popup/BaseWarningPopup/BaseWarningPopup'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/global.type'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useAudioManagement } from '../../hooks/useAudioManagement'
import { useParseTemplate } from '../../hooks/useParseTemplate'
import { usePlayAudio } from '../../hooks/usePlayAudio'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { TextToSpeechPayload } from '../../types'

export const BoxSentences = () => {
  const { t } = useTranslation()

  const {
    audio,
    getSentenceAudio,
    getSentence,
    getKeywords,
    updateStatusEditMode,
    setSentence,
    setKeywords,
  } = useMedicalFacultiesStore()

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const { onGetAudio } = useAudioManagement()

  const { openBaseWarningPopup } = useOpenBaseWarningPopup()
  console.log('getSentence()', getSentence())
  console.log('getKeywords()', getKeywords())
  const {
    handlePlayAudio: playAudioFromUrl,
    pauseAudio,
    stopAudio,
    isLoading: isAudioLoading,
    isPlaying,
    error: audioError,
    clearError,
    currentAudioUrl,
  } = usePlayAudio()

  const [loadingLanguages, setLoadingLanguages] = useState<Set<LocaleEnum>>(new Set())

  const pendingAutoPlayRef = useRef<LocaleEnum | null>(null)

  const isLanguagePlaying = (language: LocaleEnum) => {
    const audioUrl = audio[language]?.audioUrl
    return isPlaying && currentAudioUrl === audioUrl
  }

  const isLanguageLoadingPlayer = (language: LocaleEnum) => {
    const audioUrl = audio[language]?.audioUrl
    return isAudioLoading && currentAudioUrl === audioUrl
  }

  useEffect(() => {
    const pendingLanguage = pendingAutoPlayRef.current
    if (pendingLanguage && audio[pendingLanguage]?.audioUrl) {
      const audioUrl = audio[pendingLanguage].audioUrl

      pendingAutoPlayRef.current = null
      setLoadingLanguages((prev) => {
        const newSet = new Set(prev)
        newSet.delete(pendingLanguage)
        return newSet
      })

      playAudioFromUrl(audioUrl)
    }
  }, [audio, playAudioFromUrl])

  const keyWordMap = useMemo(() => {
    return Object.values(getKeywords()).reduce(
      (acc, keyword) => {
        acc[keyword.id] = keyword.name as unknown as LocalizeField<string>
        return acc
      },
      {} as Record<string, LocalizeField<string>>,
    )
  }, [getKeywords])

  const handleConfirmDelete = (close?: () => void) => {
    updateStatusEditMode(false)
    setSentence({})
    setKeywords({})

    close?.()
  }

  const OpenConfirmDelete = () => {
    openBaseWarningPopup({
      title: t('MES-1045'),
      description: t('MES-1046'),
      confirmText: t('MES-169'),
      onPressConfirm: (close) => handleConfirmDelete(close),
    })
  }

  const handlePlayAudio = async (language: LocaleEnum) => {
    if (audioError) {
      clearError()
    }

    const audioUrl = audio[language]?.audioUrl

    if (audioUrl) {
      if (isLanguagePlaying(language)) {
        await pauseAudio()
        return
      }

      if (isPlaying) {
        await stopAudio()
      }

      const success = await playAudioFromUrl(audioUrl)
      if (!success) {
        console.error('Failed to play audio:', audioError)
      }
      return
    }

    setLoadingLanguages((prev) => {
      const newSet = new Set(prev)
      newSet.add(language)
      return newSet
    })

    pendingAutoPlayRef.current = language

    const payload: TextToSpeechPayload = {
      text: getSentenceAudio(language),
      language,
      includeMedia: true,
      includeAudioFile: true,
    }

    onGetAudio(payload, language)
  }

  return (
    <View className="mt-4 w-full gap-3">
      {/* Audio Error Display */}
      {audioError && (
        <View className="mb-2 rounded-lg border border-red-300 bg-red-100 p-3">
          <View className="flex-row items-center justify-between">
            <Text size="body7" className="flex-1 text-red-600">
              🔊 {audioError}
            </Text>
            <TouchableOpacity onPress={clearError} className="ml-2 rounded bg-red-500 px-2 py-1">
              <Text size="body8" className="text-white">
                ✕
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Tiếng việt */}
      <View className="flex-row items-center justify-between gap-2">
        <Text size="body6">{primaryLanguage === LocaleEnum.VI ? t('MES-46') : t('MES-47')}</Text>

        <TouchableOpacity
          onPress={() => handlePlayAudio(primaryLanguage as LocaleEnum)}
          disabled={
            loadingLanguages.has(primaryLanguage as LocaleEnum) ||
            isLanguageLoadingPlayer(primaryLanguage as LocaleEnum)
          }
          className={`p-2 ${loadingLanguages.has(primaryLanguage as LocaleEnum) || isLanguageLoadingPlayer(primaryLanguage as LocaleEnum) ? 'opacity-50' : ''}`}
        >
          {loadingLanguages.has(primaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-blue-500">
              <ActivityIndicator size="small" />
            </Text>
          ) : isLanguageLoadingPlayer(primaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-orange-500">
              <ActivityIndicator size="small" />
            </Text>
          ) : isLanguagePlaying(primaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-green-500">
              <SoundMute height={20} width={20} className="size-5" />
            </Text>
          ) : (
            <VolumeIcon className="size-5" />
          )}
        </TouchableOpacity>
      </View>
      <View className="gap-4 rounded-lg bg-[#F9F9FC] p-4">
        <TemplateRenderer
          locale={primaryLanguage as LocaleEnum}
          keyMap={keyWordMap}
          template={getSentence()[primaryLanguage]}
        />
        <View className="flex-row items-center justify-between gap-2">
          <View></View>
          {/* <QuickEditor /> */}
          <View className="flex-row items-center gap-3">
            <TouchableOpacity
              onPress={() => {
                updateStatusEditMode(false)
              }}
            >
              <EditIcon className="size-5" />
            </TouchableOpacity>
            <TouchableOpacity onPress={OpenConfirmDelete}>
              <TrashIcon className="size-5" />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Tiếng nhật */}

      <View className="flex-row items-center justify-between gap-2">
        <Text size="body6">{secondaryLanguage === LocaleEnum.VI ? t('MES-46') : t('MES-47')}</Text>

        <TouchableOpacity
          onPress={() => handlePlayAudio(secondaryLanguage as LocaleEnum)}
          disabled={
            loadingLanguages.has(secondaryLanguage as LocaleEnum) ||
            isLanguageLoadingPlayer(secondaryLanguage as LocaleEnum)
          }
          className={`${loadingLanguages.has(secondaryLanguage as LocaleEnum) || isLanguageLoadingPlayer(secondaryLanguage as LocaleEnum) ? 'opacity-50' : ''}`}
        >
          {loadingLanguages.has(secondaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-blue-500">
              <ActivityIndicator size="small" />
            </Text>
          ) : isLanguageLoadingPlayer(secondaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-orange-500">
              <ActivityIndicator size="small" />
            </Text>
          ) : isLanguagePlaying(secondaryLanguage as LocaleEnum) ? (
            <Text size="body8" className="text-green-500">
              <SoundMute height={20} width={20} className="size-5" />
            </Text>
          ) : (
            <VolumeIcon className="size-5" />
          )}
        </TouchableOpacity>
      </View>
      <View className="gap-4 rounded-lg bg-[#F9F9FC] p-4">
        <TemplateRenderer
          locale={secondaryLanguage as LocaleEnum}
          keyMap={keyWordMap}
          template={getSentence()[secondaryLanguage]}
        />
      </View>
    </View>
  )
}

const TemplateRenderer: React.FC<{
  template: string
  keyMap: Record<string, LocalizeField<string>>
  locale: LocaleEnum
}> = ({ template, keyMap, locale }) => {
  console.log('keyMap', keyMap)
  const { parseTemplate } = useParseTemplate()

  const parsedParts = useMemo(() => parseTemplate(template), [template, parseTemplate])

  console.log(parsedParts)
  return (
    <Text size="body7">
      {parsedParts.map((part, index) => {
        if (part.type === 'text') {
          return (
            <Text size="body7" key={index}>
              {part.content}
            </Text>
          )
        }

        const value = keyMap[part?.id || '']

        if (value) {
          return (
            <Text key={index} size="body10" variant="primary">
              {value[locale]}
            </Text>
          )
        } else {
          return (
            <Text size="body7" key={index}>
              {part.id}
            </Text>
          )
        }
      })}
    </Text>
  )
}
