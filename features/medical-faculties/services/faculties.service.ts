import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'

import { TextToSpeech } from '@/types/audio.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { sentenceByAIPayload } from '../hooks/useGenerateSentenceByAI'
import { MedicalSentenceResponse } from '../hooks/useSaveMedicalSentence'
import { MedicalSentencePayload, TextToSpeechPayload } from '../types'

// SERVER / CLIENT
class MedicalFacultyService {
  private static instance: MedicalFacultyService

  private constructor() { }

  public static getInstance(): MedicalFacultyService {
    if (!MedicalFacultyService.instance) {
      MedicalFacultyService.instance = new MedicalFacultyService()
    }
    return MedicalFacultyService.instance
  }

  async getMedicalFaculties({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Faculty>> {
    const data = await httpService.get<PaginatedDocs<Faculty>>(`/${API_ENDPOINTS.faculties_api}`, {
      params,
      ...options,
    })
    return data
  }

  async getTextToSpeech({
    payload,
    options = {},
  }: {
    payload: TextToSpeechPayload
    options?: AxiosRequestConfig
  }): Promise<TextToSpeech> {
    const data = await httpService.post<TextToSpeech>(
      `/${API_ENDPOINTS.text_to_speech_api}`,
      payload,
      options,
    )
    return data
  }

  async generateSentenceByAI({
    payload,
    options = {},
  }: {
    payload: sentenceByAIPayload
    options?: AxiosRequestConfig
  }): Promise<any> {
    const data = await httpService.post<any>(
      `/ai-bot/dify/medical-sentence/analyze`,
      payload,
      options,
    )
    return data
  }

  async saveMedicalSentence({
    facultyId,
    payload,
    options = {},
  }: {
    facultyId: string
    payload: MedicalSentencePayload
    options?: AxiosRequestConfig
  }): Promise<MedicalSentenceResponse> {
    const data = await httpService.post<MedicalSentenceResponse>(
      `/${API_ENDPOINTS.medical_sentence_api}/submit/${facultyId}`,
      payload,
      options,
    )
    return data
  }

  async updateMedicalSentence({
    medicalSentenceId,
    payload,
    options = {},
  }: {
    medicalSentenceId: string
    payload: MedicalSentencePayload
    options?: AxiosRequestConfig
  }): Promise<any> {
    const data = await httpService.patch<any>(
      `/${API_ENDPOINTS.medical_sentence_api}/update/${medicalSentenceId}`,
      payload,
      options,
    )
    return data
  }
}

export const medicalFacultyService = MedicalFacultyService.getInstance()
