import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'
import { MedicalSentencePayload } from '../types'
import { useSaveMedicalSentence } from './useSaveMedicalSentence'
import { useUpdateMedicalSentence } from './useUpdateMedicalSentence'

export const useMedicalSentenceStore = () => {
  const { idReport, saveIdRecord } = useMedicalFacultiesStore()

  const { saveMedicalSentenceMutation } = useSaveMedicalSentence({
    options: {
      onSuccess: (data: any) => {
        const idRecord = data.result.id
        saveIdRecord(idRecord)
      },
      onError: () => {
        console.error('Failed to save medical sentence')
      },
    },
  })

  const { updateMedicalSentenceMutation } = useUpdateMedicalSentence()

  const saveMedicalSentence = (payload: MedicalSentencePayload, facultyId: string) => {
    if (idReport) {
      updateMedicalSentenceMutation({ payload, medicalSentenceId: idReport })
    } else {
      saveMedicalSentenceMutation({ payload, facultyId })
    }
  }

  return {
    saveMedicalSentence,
  }
}
