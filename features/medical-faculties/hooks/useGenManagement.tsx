import { Keyword } from '@/types/keyword.type'
import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'
import { sentenceByAIPayload, useGenerateSentenceByAI } from './useGenerateSentenceByAI'

export const useGenManagement = () => {
  const { generateSentenceByAIMutation } = useGenerateSentenceByAI()

  const { hiddenLoading, setSentence, setKeywords, updateStatusEditMode } =
    useMedicalFacultiesStore()

  const generateSentence = (payload: sentenceByAIPayload) => {
    generateSentenceByAIMutation(payload, {
      onSuccess: (data) => {
        const { speech, keywords } = data

        setSentence(speech)
        setKeywords(
          (keywords as Keyword[]).reduce(
            (acc, keyword) => {
              acc[keyword.id] = keyword
              return acc
            },
            {} as Record<string, any>,
          ),
        )
        updateStatusEditMode(true)

        hiddenLoading()
      },
      onError: (error) => {
        console.error('Error generating sentence:', error)
      },
    })
  }

  return {
    generateSentence,
  }
}
